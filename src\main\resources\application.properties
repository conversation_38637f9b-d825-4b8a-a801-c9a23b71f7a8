spring.application.name=springboot-db

# Server configuration
server.port=8080

# Configuration pour MySQL Database (XAMPP)
spring.datasource.url=************************************************************************************************************************************
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
spring.datasource.username=root
spring.datasource.password=

# JPA/Hibernate properties
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
# Remove explicit dialect - let Hibernate auto-detect to avoid deprecation warning
# spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect

# Additional JPA settings for better compatibility
spring.jpa.open-in-view=false


